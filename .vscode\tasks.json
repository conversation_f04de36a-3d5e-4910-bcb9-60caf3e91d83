{"version": "2.0.0", "tasks": [{"type": "cppbuild", "label": "C/C++: g++.exe build active file", "command": "g++", "args": ["-fdiagnostics-color=always", "-g", "-IC:/mingw/include", "-IC:/mingw/include/GL", "${file}", "-o", "${fileDirname}\\${fileBasenameNoExtension}.exe", "-lopengl32", "-lglu32", "-lfreeglut"], "options": {"cwd": "${fileDirname}"}, "problemMatcher": ["$gcc"], "group": {"kind": "build", "isDefault": true}, "detail": "compiler: g++.exe"}]}