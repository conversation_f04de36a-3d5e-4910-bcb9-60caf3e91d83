{"version": "2.0.0", "tasks": [{"type": "cppbuild", "label": "Build Gundam Head Project", "command": "g++", "args": ["-std=c++11", "-Wall", "-g", "-I${workspaceFolder}/freeglut/include", "${workspaceFolder}/main.cpp", "-o", "${workspaceFolder}/GundamHead.exe", "-L${workspaceFolder}/freeglut/lib", "-lfreeglut", "-lopengl32", "-lglu32", "-lwin<PERSON>", "-lgdi32"], "options": {"cwd": "${workspaceFolder}"}, "problemMatcher": ["$gcc"], "group": {"kind": "build", "isDefault": true}, "detail": "Build Gundam Head with FreeGLUT"}, {"type": "shell", "label": "Run Gundam Head", "command": "${workspaceFolder}/GundamHead.exe", "group": "test", "dependsOn": "Build Gundam Head Project", "options": {"cwd": "${workspaceFolder}"}}, {"type": "shell", "label": "Build with CMake", "command": "cmake", "args": ["--build", "build", "--config", "Debug"], "group": "build", "options": {"cwd": "${workspaceFolder}"}, "dependsOn": "Configure CMake"}, {"type": "shell", "label": "Configure CMake", "command": "cmake", "args": ["-B", "build", "-S", "."], "group": "build", "options": {"cwd": "${workspaceFolder}"}}]}