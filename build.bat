@echo off
echo Building Gundam Head Project...
echo.

REM Check if g++ is available
g++ --version >nul 2>&1
if errorlevel 1 (
    echo Error: g++ compiler not found!
    echo Please install MinGW-w64 or MSYS2 and add it to PATH.
    pause
    exit /b 1
)

REM Check if FreeGLUT directory exists
if not exist "freeglut\include\GL\freeglut.h" (
    echo Error: FreeGLUT not found!
    echo Please make sure freeglut folder exists with proper structure:
    echo   freeglut\include\GL\freeglut.h
    echo   freeglut\lib\libfreeglut.a or freeglut.lib
    pause
    exit /b 1
)

REM Compile the project
echo Compiling...
g++ -std=c++11 -Wall -g -I./freeglut/include main.cpp -o GundamHead.exe -L./freeglut/lib -lfreeglut -lopengl32 -lglu32 -lwinmm -lgdi32

if errorlevel 1 (
    echo.
    echo Compilation failed!
    pause
    exit /b 1
)

echo.
echo Compilation successful!
echo Executable: GundamHead.exe
echo.
echo To run the program, type: GundamHead.exe
echo Or double-click the executable file.
pause
