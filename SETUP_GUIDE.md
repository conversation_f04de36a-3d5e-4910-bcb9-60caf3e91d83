# Setup Guide - Gundam RX-78-2 Head Project

## Langkah-langkah Setup Lengkap

### 1. Persiapan Environment

#### Install MinGW-w64 atau MSYS2

**Opsi A: MSYS2 (Recommended)**
1. Download MSYS2 dari https://www.msys2.org/
2. Install dan jalankan MSYS2 terminal
3. Update package database:
   ```bash
   pacman -Syu
   ```
4. Install development tools:
   ```bash
   pacman -S mingw-w64-x86_64-gcc
   pacman -S mingw-w64-x86_64-freeglut
   pacman -S make
   ```
5. Tambahkan ke PATH: `C:\msys64\mingw64\bin`

**Opsi B: MinGW-w64 Standalone**
1. Download dari https://www.mingw-w64.org/
2. Install dan tambahkan ke PATH
3. Download FreeGLUT secara terpisah

### 2. Setup FreeGLUT

#### Jika menggunakan MSYS2:
FreeGLUT sudah terinstall, tapi perlu copy ke project folder:

```bash
# Buat folder structure
mkdir freeglut
mkdir freeglut/include
mkdir freeglut/lib
mkdir freeglut/bin

# Copy files dari MSYS2
cp -r /mingw64/include/GL freeglut/include/
cp /mingw64/lib/libfreeglut* freeglut/lib/
cp /mingw64/bin/freeglut.dll freeglut/bin/
```

#### Jika download manual:
1. Download FreeGLUT dari http://freeglut.sourceforge.net/
2. Extract dan copy files:
   ```
   freeglut/
   ├── include/
   │   └── GL/
   │       ├── freeglut.h
   │       ├── freeglut_ext.h
   │       ├── freeglut_std.h
   │       └── glut.h
   ├── lib/
   │   ├── libfreeglut.a (atau freeglut.lib)
   │   └── libfreeglut_static.a
   └── bin/
       └── freeglut.dll
   ```

### 3. Verifikasi Setup

Test compiler:
```bash
g++ --version
```

Test FreeGLUT:
```bash
g++ -I./freeglut/include -L./freeglut/lib -lfreeglut -lopengl32 -lglu32 --version
```

### 4. Kompilasi Project

#### Menggunakan VS Code:
1. Buka folder project di VS Code
2. Tekan `Ctrl+Shift+P`
3. Pilih "Tasks: Run Build Task"
4. Pilih "Build Gundam Head Project"

#### Menggunakan Command Line:

**Windows Command Prompt:**
```cmd
build.bat
```

**MSYS2/Git Bash:**
```bash
make
```

**Manual compilation:**
```bash
g++ -std=c++11 -Wall -g -I./freeglut/include main.cpp -o GundamHead.exe -L./freeglut/lib -lfreeglut -lopengl32 -lglu32 -lwinmm -lgdi32
```

### 5. Troubleshooting

#### Error: "freeglut.h: No such file or directory"
- Pastikan folder `freeglut/include/GL/freeglut.h` ada
- Periksa path di compiler command

#### Error: "cannot find -lfreeglut"
- Pastikan file library ada di `freeglut/lib/`
- Untuk MinGW: `libfreeglut.a`
- Untuk MSVC: `freeglut.lib`

#### Error: "freeglut.dll missing"
- Copy `freeglut.dll` ke folder project
- Atau tambahkan `freeglut/bin` ke PATH

#### Error: "undefined reference to WinMain"
- Pastikan menggunakan flag `-lgdi32 -lwinmm`
- Pastikan main function ada

#### Compilation berhasil tapi program tidak jalan:
- Pastikan `freeglut.dll` ada di folder yang sama dengan .exe
- Atau install Visual C++ Redistributable

### 6. Alternative: Menggunakan Dev-C++

Jika VS Code sulit di-setup:

1. Download Dev-C++ dari https://www.bloodshed.net/devcpp.html
2. Buka Dev-C++
3. File → New → Project → Console Application
4. Copy paste code dari `main.cpp`
5. Project → Project Options → Parameters
6. Linker: tambahkan `-lopengl32 -lglu32 -lfreeglut`
7. Directories: tambahkan path ke FreeGLUT include dan lib
8. Compile & Run

### 7. Alternative: Code::Blocks

1. Download Code::Blocks dengan MinGW
2. Settings → Compiler → Search directories
3. Tambahkan FreeGLUT include dan lib paths
4. Project → Build options → Linker settings
5. Tambahkan libraries: opengl32, glu32, freeglut

### 8. Menjalankan Program

Setelah kompilasi berhasil:

```bash
./GundamHead.exe
```

Atau double-click file `GundamHead.exe`

### 9. Expected Output

Program akan menampilkan window dengan:
- Kepala Gundam RX-78-2 yang simetris
- Warna: Putih (helm), Merah (dagu, dahi), Kuning (mata, V-fin)
- Background gelap
- Window size 600x600 pixels

### 10. Controls

- **ESC**: Keluar dari program
- **Alt+F4**: Keluar dari program
- **Close button**: Keluar dari program

Program ini adalah static display, tidak ada interaksi mouse/keyboard lainnya.

## File Structure Akhir

```
project/
├── main.cpp                 # Source code utama
├── GundamHead.exe           # Executable (setelah compile)
├── freeglut.dll             # Runtime library (jika perlu)
├── freeglut/                # FreeGLUT library
│   ├── include/GL/          # Header files
│   ├── lib/                 # Library files  
│   └── bin/                 # DLL files
├── .vscode/                 # VS Code config
├── build.bat                # Windows build script
├── run.bat                  # Windows run script
├── Makefile                 # Make build script
├── CMakeLists.txt           # CMake config
├── README.md                # Documentation
├── SETUP_GUIDE.md           # This file
└── project.json             # Project metadata
```

## Bantuan Tambahan

Jika masih ada masalah, periksa:

1. **PATH Environment Variable**: Pastikan g++ dan tools ada di PATH
2. **Architecture Match**: 32-bit vs 64-bit harus konsisten
3. **Library Version**: Pastikan FreeGLUT compatible dengan compiler
4. **Antivirus**: Beberapa antivirus block executable compilation
5. **Permissions**: Pastikan folder project writable

Untuk bantuan lebih lanjut, cek error message dan Google dengan keyword:
- "freeglut windows setup"
- "opengl mingw compilation"
- "visual studio code c++ opengl"
