cmake_minimum_required(VERSION 3.10)
project(GundamHead)

# Set C++ standard
set(CMAKE_CXX_STANDARD 11)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Find OpenGL
find_package(OpenGL REQUIRED)

# Set the path to FreeGLUT (adjust this path based on your FreeGLUT installation)
set(FREEGLUT_ROOT_PATH "${CMAKE_CURRENT_SOURCE_DIR}/freeglut")

# Include directories
include_directories(${FREEGLUT_ROOT_PATH}/include)
include_directories(${OPENGL_INCLUDE_DIRS})

# Link directories
link_directories(${FREEGLUT_ROOT_PATH}/lib)

# Add executable
add_executable(GundamHead main.cpp)

# Link libraries
if(WIN32)
    target_link_libraries(GundamHead 
        ${OPENGL_LIBRARIES}
        freeglut
        glu32
        opengl32
    )
else()
    target_link_libraries(GundamHead 
        ${OPENGL_LIBRARIES}
        glut
        GLU
        GL
    )
endif()

# Set output directory
set_target_properties(GundamHead PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}
)
