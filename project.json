{"name": "Gundam RX-78-2 Head Project", "description": "Computer Graphics project rendering Gundam RX-78-2 head using OpenGL and FreeGLUT", "version": "1.0.0", "author": "Computer Graphics Student", "language": "C++", "standard": "C++11", "graphics": {"api": "OpenGL", "library": "FreeGLUT", "techniques": ["Mirroring", "Sc<PERSON>", "Rotation", "Recursion", "Translation", "<PERSON><PERSON> Curves", "Polygon Tessellation"]}, "shapes": ["Rectangle/Square", "Triangle", "Polygon", "Curve", "Line", "Circle"], "components": {"head_dome": "Curved polygon using <PERSON><PERSON> curves", "helmet_sides": "White polygons with mirroring", "vulcan_guns": "Circles and triangular housings", "side_vents": "Recursive rectangular patterns", "cheek_guards": "Complex polygons", "mouth_plate": "Gray polygon with vent slits", "visor": "Black polygon for eye area", "eyes": "Yellow polygons", "chin": "Red triangular polygon", "forehead_crest": "Red polygon with scaling", "forehead_camera": "White and red polygons", "v_fins": "Yellow triangular polygons with rotation"}, "build": {"compiler": "g++", "flags": ["-std=c++11", "-Wall", "-g"], "includes": ["./freeglut/include"], "libraries": ["freeglut", "opengl32", "glu32", "winmm", "gdi32"], "output": "GundamHead.exe"}, "files": {"source": "main.cpp", "build_scripts": ["build.bat", "run.bat", "<PERSON><PERSON><PERSON>"], "config": ["CMakeLists.txt", ".vscode/tasks.json", ".vscode/c_cpp_properties.json"], "documentation": ["README.md", "project.json"]}}