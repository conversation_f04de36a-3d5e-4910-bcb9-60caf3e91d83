#include <windows.h>
#include <GL/glut.h>
#include <cmath>
#include <vector>
#include <utility>

// Konstanta PI untuk kalkulasi
const float PI = 3.1415926f;

// --- FUNGSI UTILITAS GAMBAR ---

/**
 * Menggambar poligon berisi dari sebuah vektor titik.
 */
void drawPolygon(const std::vector<std::pair<float, float> >& points) {
    glBegin(GL_POLYGON);
    for (size_t i = 0; i < points.size(); ++i) {
        glVertex2f(points[i].first, points[i].second);
    }
    glEnd();
}

/**
 * Menggambar garis luar poligon dari sebuah vektor titik.
 */
void drawPolygonOutline(const std::vector<std::pair<float, float> >& points, float width = 2.0f) {
    glLineWidth(width);
    glColor3f(0.0f, 0.0f, 0.0f); // Garis luar hitam
    glBegin(GL_LINE_LOOP);
    for (size_t i = 0; i < points.size(); ++i) {
        glVertex2f(points[i].first, points[i].second);
    }
    glEnd();
}

/**
 * Menggambar lingkaran berisi.
 */
void drawCircle(float cx, float cy, float r, int segments = 36) {
    glBegin(GL_POLYGON);
    for (int i = 0; i < segments; ++i) {
        float theta = 2.0f * PI * float(i) / float(segments);
        float x = r * cosf(theta);
        float y = r * sinf(theta);
        glVertex2f(x + cx, y + cy);
    }
    glEnd();
}

/**
 * Menggambar persegi dengan translasi dan skala
 */
void drawRectangle(float centerX, float centerY, float width, float height) {
    std::vector<std::pair<float, float> > rect;
    rect.push_back(std::make_pair(centerX - width/2, centerY + height/2));
    rect.push_back(std::make_pair(centerX + width/2, centerY + height/2));
    rect.push_back(std::make_pair(centerX + width/2, centerY - height/2));
    rect.push_back(std::make_pair(centerX - width/2, centerY - height/2));
    drawPolygon(rect);
    drawPolygonOutline(rect);
}

/**
 * Menggambar segitiga dengan rotasi
 */
void drawTriangle(float x1, float y1, float x2, float y2, float x3, float y3) {
    std::vector<std::pair<float, float> > triangle;
    triangle.push_back(std::make_pair(x1, y1));
    triangle.push_back(std::make_pair(x2, y2));
    triangle.push_back(std::make_pair(x3, y3));
    drawPolygon(triangle);
    drawPolygonOutline(triangle);
}

/**
 * Menggambar garis
 */
void drawLine(float x1, float y1, float x2, float y2, float width = 2.0f) {
    glLineWidth(width);
    glBegin(GL_LINES);
    glVertex2f(x1, y1);
    glVertex2f(x2, y2);
    glEnd();
}

/**
 * Menghasilkan titik untuk kurva Bezier kubik dan menggabungkannya dengan titik lain
 * untuk menggambar satu poligon tertutup. Digunakan untuk kubah kepala yang melengkung.
 */
void drawCurvedPolygon(const std::vector<std::pair<float, float> >& curve_pts, const std::vector<std::pair<float, float> >& line_pts, int segments = 30) {
    std::vector<std::pair<float, float> > allPoints;

    // Menghasilkan titik untuk bagian kurva Bezier
    for (int i = 0; i <= segments; ++i) {
        float t = (float)i / (float)segments;
        float u = 1.0f - t;
        float tt = t * t;
        float uu = u * u;
        float uuu = uu * u;
        float ttt = tt * t;

        // Rumus Bezier kubik
        float x = uuu * curve_pts[0].first + 3 * uu * t * curve_pts[1].first + 3 * u * tt * curve_pts[2].first + ttt * curve_pts[3].first;
        float y = uuu * curve_pts[0].second + 3 * uu * t * curve_pts[1].second + 3 * u * tt * curve_pts[2].second + ttt * curve_pts[3].second;
        allPoints.push_back(std::make_pair(x, y));
    }
    
    // Menambahkan titik-titik garis lurus untuk menutup bentuk
    for(size_t i = 0; i < line_pts.size(); ++i) {
        allPoints.push_back(line_pts[i]);
    }

    drawPolygon(allPoints);
    drawPolygonOutline(allPoints);
}

/**
 * Teknik Rekursif: Menggambar ventilasi samping kepala Gundam secara berulang.
 */
void drawSideVents(float startX, float startY, int count) {
    if (count <= 0) return;

    // Menggambar persegi kecil untuk ventilasi
    glColor3f(0.2f, 0.2f, 0.2f); // Abu-abu gelap
    drawRectangle(startX + 0.025f, startY - 0.025f, 0.04f, 0.03f);
    
    // Garis ventilasi di dalam persegi
    glColor3f(0.0f, 0.0f, 0.0f);
    drawLine(startX + 0.01f, startY - 0.02f, startX + 0.04f, startY - 0.02f, 1.0f);
    drawLine(startX + 0.01f, startY - 0.03f, startX + 0.04f, startY - 0.03f, 1.0f);

    // Rekursi untuk ventilasi berikutnya
    drawSideVents(startX - 0.02f, startY - 0.08f, count - 1);
}

/**
 * Menggambar ventilasi mulut dengan pola berulang
 */
void drawMouthVents() {
    float centerX = 0.0f;
    float startY = -0.25f;
    
    for (int i = 0; i < 4; ++i) {
        float y = startY - i * 0.04f;
        glColor3f(0.0f, 0.0f, 0.0f);
        drawRectangle(centerX, y, 0.12f, 0.02f);
    }
}

// --- LOGIKA GAMBAR UTAMA ---

void drawLeftHalf() {
    // 1. Kubah Kepala Utama (Putih) dengan atap melengkung (Kurva Bezier)
    {
        std::vector<std::pair<float, float> > curve_points;
        curve_points.push_back(std::make_pair(-0.35f, 0.25f));
        curve_points.push_back(std::make_pair(-0.35f, 0.8f));
        curve_points.push_back(std::make_pair(-0.1f, 0.85f));
        curve_points.push_back(std::make_pair(0.0f, 0.8f));

        std::vector<std::pair<float, float> > line_points;
        line_points.push_back(std::make_pair(0.0f, 0.15f));
        line_points.push_back(std::make_pair(-0.25f, 0.2f));

        glColor3f(0.95f, 0.95f, 0.95f); // Putih sedikit keabu-abuan
        drawCurvedPolygon(curve_points, line_points);
    }
    
    // 2. Sisi Helm (Putih) - Polygon yang lebih akurat
    std::vector<std::pair<float, float> > helmet;
    helmet.push_back(std::make_pair(-0.35f, 0.25f));
    helmet.push_back(std::make_pair(-0.65f, 0.2f));
    helmet.push_back(std::make_pair(-0.7f, -0.3f));
    helmet.push_back(std::make_pair(-0.55f, -0.55f));
    helmet.push_back(std::make_pair(-0.35f, -0.5f));
    helmet.push_back(std::make_pair(-0.25f, 0.2f));
    
    glColor3f(0.95f, 0.95f, 0.95f);
    drawPolygon(helmet);
    drawPolygonOutline(helmet);
    
    // 3. Vulcan Gun Pod (Segitiga + Lingkaran)
    // Base segitiga
    glColor3f(0.9f, 0.9f, 0.9f);
    drawTriangle(-0.35f, 0.25f, -0.42f, 0.35f, -0.32f, 0.4f);
    
    // Lingkaran kuning untuk barrel
    glColor3f(1.0f, 0.9f, 0.1f);
    drawCircle(-0.37f, 0.33f, 0.02f);
    
    // Lingkaran hitam kecil di tengah
    glColor3f(0.0f, 0.0f, 0.0f);
    drawCircle(-0.37f, 0.33f, 0.008f);

    // 4. Ventilasi Samping (Rekursif)
    drawSideVents(-0.6f, 0.05f, 4);
    
    // 5. Pelindung Pipi (Putih) - Polygon yang lebih detail
    std::vector<std::pair<float, float> > cheek;
    cheek.push_back(std::make_pair(-0.25f, 0.2f));
    cheek.push_back(std::make_pair(-0.4f, 0.15f));
    cheek.push_back(std::make_pair(-0.42f, -0.25f));
    cheek.push_back(std::make_pair(-0.25f, -0.35f));
    cheek.push_back(std::make_pair(0.0f, -0.25f));
    cheek.push_back(std::make_pair(0.0f, 0.15f));
    
    glColor3f(0.95f, 0.95f, 0.95f);
    drawPolygon(cheek);
    drawPolygonOutline(cheek);

    // 6. Area Mulut (Abu-abu) - Polygon
    std::vector<std::pair<float, float> > mouthArea;
    mouthArea.push_back(std::make_pair(0.0f, -0.15f));
    mouthArea.push_back(std::make_pair(-0.2f, -0.18f));
    mouthArea.push_back(std::make_pair(-0.18f, -0.4f));
    mouthArea.push_back(std::make_pair(0.0f, -0.38f));
    
    glColor3f(0.75f, 0.75f, 0.75f);
    drawPolygon(mouthArea);
    drawPolygonOutline(mouthArea);

    // Ventilasi mulut (Persegi berulang)
    drawMouthVents();

    // 7. Visor Hitam (Polygon untuk area mata)
    std::vector<std::pair<float, float> > visor;
    visor.push_back(std::make_pair(0.0f, 0.15f));
    visor.push_back(std::make_pair(-0.35f, 0.12f));
    visor.push_back(std::make_pair(-0.32f, -0.05f));
    visor.push_back(std::make_pair(0.0f, -0.08f));
    
    glColor3f(0.1f, 0.1f, 0.2f); // Biru gelap untuk efek reflektif
    drawPolygon(visor);

    // 8. Mata (Kuning) - Polygon yang lebih akurat
    std::vector<std::pair<float, float> > eye;
    eye.push_back(std::make_pair(-0.28f, 0.08f));
    eye.push_back(std::make_pair(-0.12f, 0.06f));
    eye.push_back(std::make_pair(-0.15f, -0.02f));
    eye.push_back(std::make_pair(-0.28f, 0.0f));
    
    glColor3f(1.0f, 0.9f, 0.1f); // Kuning cerah
    drawPolygon(eye);
    drawPolygonOutline(eye);

    // Highlight mata (Lingkaran kecil)
    glColor3f(1.0f, 1.0f, 0.8f);
    drawCircle(-0.2f, 0.04f, 0.015f);

    // 9. Dagu (Merah) - Segitiga
    glColor3f(0.9f, 0.1f, 0.1f);
    drawTriangle(0.0f, -0.25f, -0.18f, -0.35f, 0.0f, -0.5f);

    // 10. Lambang Dahi (Merah) - Polygon
    std::vector<std::pair<float, float> > crest;
    crest.push_back(std::make_pair(0.0f, 0.15f));
    crest.push_back(std::make_pair(-0.15f, 0.3f));
    crest.push_back(std::make_pair(-0.15f, 0.5f));
    crest.push_back(std::make_pair(0.0f, 0.6f));
    
    glColor3f(0.9f, 0.1f, 0.1f);
    drawPolygon(crest);
    drawPolygonOutline(crest);
    
    // Detail garis pada lambang dahi
    glColor3f(0.7f, 0.0f, 0.0f);
    drawLine(-0.05f, 0.25f, -0.12f, 0.45f, 1.5f);

    // 11. Kamera Dahi (Putih dengan pusat merah) - Persegi + Lingkaran
    glColor3f(0.95f, 0.95f, 0.95f);
    drawRectangle(-0.075f, 0.68f, 0.15f, 0.12f);
    
    // Lensa kamera (Lingkaran merah)
    glColor3f(0.9f, 0.1f, 0.1f);
    drawCircle(-0.075f, 0.68f, 0.04f);
    
    // Highlight lensa
    glColor3f(1.0f, 0.8f, 0.8f);
    drawCircle(-0.065f, 0.7f, 0.015f);

    // 12. V-Fin (Kuning) - Segitiga dengan rotasi
    // V-Fin kiri (dengan sedikit rotasi)
    glColor3f(1.0f, 0.9f, 0.1f);
    drawTriangle(-0.12f, 0.5f, -0.18f, 0.52f, -0.6f, 0.9f);
    
    // Garis detail pada V-Fin
    glColor3f(0.8f, 0.7f, 0.0f);
    drawLine(-0.15f, 0.51f, -0.55f, 0.85f, 1.5f);
    
    // Base V-Fin (Persegi kecil hitam)
    glColor3f(0.2f, 0.2f, 0.2f);
    drawRectangle(-0.15f, 0.48f, 0.06f, 0.04f);
    
    // 13. Detail tambahan - Garis panel
    glColor3f(0.6f, 0.6f, 0.6f);
    drawLine(-0.2f, 0.1f, -0.05f, 0.12f, 1.0f); // Garis horizontal
    drawLine(-0.3f, 0.0f, -0.15f, -0.15f, 1.0f); // Garis diagonal
    drawLine(-0.5f, -0.1f, -0.3f, -0.3f, 1.0f);  // Garis sisi
}

// --- FUNGSI CALLBACK DAN SETUP GLUT ---

void display() {
    glClear(GL_COLOR_BUFFER_BIT);

    // Gambar setengah kiri
    drawLeftHalf();

    // Teknik Mirroring: Gambar setengah kanan dengan scaling -1 pada sumbu X
    glPushMatrix();
    glScalef(-1.0f, 1.0f, 1.0f);
    drawLeftHalf();
    glPopMatrix();

    glFlush();
}

void init() {
    glClearColor(0.05f, 0.05f, 0.1f, 1.0f); // Background biru gelap seperti luar angkasa
    glMatrixMode(GL_PROJECTION);
    glLoadIdentity();
    gluOrtho2D(-1.0, 1.0, -1.0, 1.0);
    glMatrixMode(GL_MODELVIEW);
}

int main(int argc, char** argv) {
    glutInit(&argc, argv);
    glutInitDisplayMode(GLUT_SINGLE | GLUT_RGB);
    glutInitWindowSize(700, 700); // Window lebih besar untuk detail yang lebih baik
    glutCreateWindow("Gundam RX-78-2 Head - Enhanced");
    init();
    glutDisplayFunc(display);
    glutMainLoop();
    return 0;
}
