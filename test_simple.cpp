// Simple OpenGL test for Dev-C++ 5.11
// Test program to verify FreeGLUT setup

#include <windows.h>
#include <GL/glut.h>

void display() {
    glClear(GL_COLOR_BUFFER_BIT);
    
    // Draw a simple red triangle
    glColor3f(1.0f, 0.0f, 0.0f); // Red color
    glBegin(GL_TRIANGLES);
        glVertex2f(0.0f, 0.5f);   // Top vertex
        glVertex2f(-0.5f, -0.5f); // Bottom left
        glVertex2f(0.5f, -0.5f);  // Bottom right
    glEnd();
    
    // Draw a yellow circle
    glColor3f(1.0f, 1.0f, 0.0f); // Yellow color
    glBegin(GL_POLYGON);
    for (int i = 0; i < 20; ++i) {
        float angle = 2.0f * 3.14159f * i / 20;
        float x = 0.2f * cos(angle);
        float y = 0.2f * sin(angle) + 0.7f;
        glVertex2f(x, y);
    }
    glEnd();
    
    glFlush();
}

void init() {
    glClearColor(0.0f, 0.0f, 0.5f, 1.0f); // Blue background
    glMatrixMode(GL_PROJECTION);
    glLoadIdentity();
    gluOrtho2D(-1.0, 1.0, -1.0, 1.0);
    glMatrixMode(GL_MODELVIEW);
}

int main(int argc, char** argv) {
    glutInit(&argc, argv);
    glutInitDisplayMode(GLUT_SINGLE | GLUT_RGB);
    glutInitWindowSize(400, 400);
    glutCreateWindow("OpenGL Test - Dev-C++ 5.11");
    init();
    glutDisplayFunc(display);
    glutMainLoop();
    return 0;
}
