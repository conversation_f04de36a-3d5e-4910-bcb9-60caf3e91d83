# Dev-C++ 5.11 Compilation Guide - Gundam RX-78-2 Head

## Setup untuk Dev-C++ 5.11

### 1. Persiapan FreeGLUT

#### Download FreeGLUT untuk Dev-C++:
1. Download FreeGLUT dari: http://freeglut.sourceforge.net/
2. Pilih versi Windows Binary yang sesuai dengan Dev-C++ (biasanya MinGW)
3. Extract ke folder, misalnya `C:\freeglut\`

#### Struktur folder FreeGLUT:
```
C:\freeglut\
├── include\
│   └── GL\
│       ├── freeglut.h
│       ├── freeglut_ext.h
│       ├── freeglut_std.h
│       └── glut.h
├── lib\
│   ├── libfreeglut.a
│   └── libfreeglut_static.a
└── bin\
    └── freeglut.dll
```

### 2. Konfigurasi Dev-C++

#### Setup Include dan Library Paths:
1. Buka Dev-C++
2. Menu **Tools** → **Compiler Options**
3. Tab **Directories**
4. **C++ Includes**: Tambahkan `C:\freeglut\include`
5. **Libraries**: Tambahkan `C:\freeglut\lib`

#### Setup Linker:
1. <PERSON><PERSON><PERSON> di **Compiler Options**
2. Tab **Settings** → **Code Generation** → **Linker**
3. Tambahkan libraries berikut (satu per baris):
   ```
   -lopengl32
   -lglu32
   -lfreeglut
   -lgdi32
   -lwinmm
   ```

### 3. Membuat Project Baru

#### Opsi A: Menggunakan File Project (.dev)
1. Double-click file `GundamHead.dev` yang sudah disediakan
2. Dev-C++ akan membuka project dengan konfigurasi yang sudah benar

#### Opsi B: Membuat Project Manual
1. **File** → **New** → **Project**
2. Pilih **Console Application**
3. Pilih **C++ Project**
4. Beri nama "GundamHead"
5. Hapus kode template yang ada
6. Copy-paste kode dari `main_improved.cpp`

### 4. Konfigurasi Project

#### Project Options:
1. **Project** → **Project Options**
2. Tab **Parameters**
3. **Linker**: Tambahkan:
   ```
   -lopengl32
   -lglu32
   -lfreeglut
   -lgdi32
   -lwinmm
   ```
4. Tab **Directories**
5. **Include Directories**: Tambahkan path ke FreeGLUT include
6. **Library Directories**: Tambahkan path ke FreeGLUT lib

### 5. Kompilasi dan Menjalankan

#### Compile:
1. Tekan **F9** atau **Execute** → **Compile**
2. Jika ada error, periksa konfigurasi library

#### Run:
1. Tekan **F10** atau **Execute** → **Run**
2. Pastikan `freeglut.dll` ada di folder yang sama dengan executable

### 6. Troubleshooting

#### Error: "freeglut.h: No such file or directory"
- Periksa path Include Directories
- Pastikan struktur folder FreeGLUT benar

#### Error: "undefined reference to glutInit"
- Tambahkan `-lfreeglut` di Linker options
- Periksa Library Directories path

#### Error: "freeglut.dll missing"
- Copy `freeglut.dll` dari `freeglut\bin\` ke folder project
- Atau copy ke `C:\Windows\System32\`

#### Error: "undefined reference to WinMain"
- Pastikan menggunakan Console Application, bukan Windows Application
- Tambahkan `-lgdi32 -lwinmm` di linker

### 7. Fitur Program

Program ini mendemonstrasikan teknik computer graphics:

#### Shapes (Bentuk):
- **Persegi**: Helm, area mulut, ventilasi
- **Segitiga**: V-fin, vulcan gun, dagu
- **Polygon**: Pelindung pipi, visor, mata
- **Kurva**: Kubah kepala menggunakan Bezier curves
- **Line**: Detail panel, outline
- **Lingkaran**: Mata, vulcan gun, kamera

#### Techniques (Teknik):
- **Mirroring**: Kepala simetris dengan `glScalef(-1.0f, 1.0f, 1.0f)`
- **Skala**: Berbagai ukuran komponen
- **Rotasi**: V-fin dengan orientasi yang tepat
- **Rekursif**: Pola ventilasi samping
- **Translasi**: Penempatan komponen dengan koordinat

### 8. Kontrol Program

- **ESC**: Keluar dari program
- **Alt+F4**: Keluar dari program
- **Close button**: Keluar dari program

Program menampilkan static image tanpa interaksi mouse/keyboard.

### 9. Hasil yang Diharapkan

Program akan menampilkan:
- Kepala Gundam RX-78-2 yang simetris
- Warna akurat: Putih (helm), Merah (dagu, dahi), Kuning (mata, V-fin)
- Background biru gelap (space theme)
- Detail yang lebih akurat dibanding versi sebelumnya
- Window size 700x700 pixels

### 10. File yang Digunakan

- `main_improved.cpp`: Source code utama (C++98 compatible)
- `GundamHead.dev`: Dev-C++ project file
- `DEV_CPP_GUIDE.md`: Panduan ini

### Tips Tambahan

1. **Backup**: Selalu backup project sebelum mengubah konfigurasi
2. **Testing**: Test compile dengan program sederhana dulu
3. **Path**: Gunakan path absolut jika relative path bermasalah
4. **Version**: Pastikan Dev-C++ dan MinGW version compatible
5. **Antivirus**: Beberapa antivirus block OpenGL programs

Jika masih ada masalah, coba compile program test sederhana dulu untuk memastikan OpenGL setup benar.
