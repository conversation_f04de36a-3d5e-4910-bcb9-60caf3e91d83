#include <windows.h>
#include <GL/glut.h>
#include <cmath>
#include <vector>
#include <utility>
#include <cstddef>

const float PI = 3.1415926f;

// Enhanced color constants for better visual quality
struct Color {
    float r, g, b;
    Color(float red, float green, float blue) : r(red), g(green), b(blue) {}
};

// Gundam RX-78-2 Color Palette
const Color WHITE_PRIMARY(0.95f, 0.95f, 0.95f);
const Color WHITE_SECONDARY(0.85f, 0.85f, 0.85f);
const Color RED_PRIMARY(0.8f, 0.1f, 0.1f);
const Color RED_SECONDARY(0.9f, 0.2f, 0.2f);
const Color YELLOW_PRIMARY(1.0f, 0.9f, 0.1f);
const Color YELLOW_SECONDARY(0.9f, 0.8f, 0.0f);
const Color BLUE_PRIMARY(0.1f, 0.3f, 0.7f);
const Color GRAY_DARK(0.3f, 0.3f, 0.3f);
const Color GRAY_LIGHT(0.7f, 0.7f, 0.7f);
const Color BLACK(0.0f, 0.0f, 0.0f);

void setColor(const Color& color) {
    glColor3f(color.r, color.g, color.b);
}

//Menggambar poligon berisi dari sebuah vektor titik.

void drawPolygon(const std::vector<std::pair<float, float> >& points) {
    glBegin(GL_POLYGON);
    for (size_t i = 0; i < points.size(); ++i) {
        glVertex2f(points[i].first, points[i].second);
    }
    glEnd();
}

//Menggambar garis luar poligon dari sebuah vektor titik.

void drawPolygonOutline(const std::vector<std::pair<float, float> >& points, float width = 2.0f) {
    glLineWidth(width);
    setColor(BLACK); // Garis luar hitam
    glBegin(GL_LINE_LOOP);
    for (size_t i = 0; i < points.size(); ++i) {
        glVertex2f(points[i].first, points[i].second);
    }
    glEnd();
}

// Enhanced function to draw filled polygon with color
void drawFilledPolygon(const std::vector<std::pair<float, float> >& points, const Color& fillColor, bool withOutline = true) {
    setColor(fillColor);
    drawPolygon(points);
    if (withOutline) {
        drawPolygonOutline(points);
    }
}

// Function to draw gradient-like effect using multiple polygons
void drawGradientPolygon(const std::vector<std::pair<float, float> >& points, const Color& color1, const Color& color2, int steps = 3) {
    for (int i = 0; i < steps; ++i) {
        float t = (float)i / (float)(steps - 1);
        Color blendedColor(
            color1.r * (1.0f - t) + color2.r * t,
            color1.g * (1.0f - t) + color2.g * t,
            color1.b * (1.0f - t) + color2.b * t
        );

        // Create slightly smaller polygon for each step
        std::vector<std::pair<float, float> > scaledPoints;
        float scale = 1.0f - (t * 0.1f);
        for (size_t j = 0; j < points.size(); ++j) {
            scaledPoints.push_back(std::make_pair(
                points[j].first * scale,
                points[j].second * scale
            ));
        }

        setColor(blendedColor);
        drawPolygon(scaledPoints);
    }
    drawPolygonOutline(points);
}

/**
 * Menggambar lingkaran berisi.
 */
void drawCircle(float cx, float cy, float r, int segments = 36) {
    glBegin(GL_POLYGON);
    for (int i = 0; i < segments; ++i) {
        float theta = 2.0f * PI * float(i) / float(segments);
        float x = r * cosf(theta);
        float y = r * sinf(theta);
        glVertex2f(x + cx, y + cy);
    }
    glEnd();
}

// Enhanced circle with color and outline
void drawFilledCircle(float cx, float cy, float r, const Color& fillColor, bool withOutline = true, int segments = 36) {
    setColor(fillColor);
    drawCircle(cx, cy, r, segments);

    if (withOutline) {
        setColor(BLACK);
        glLineWidth(2.0f);
        glBegin(GL_LINE_LOOP);
        for (int i = 0; i < segments; ++i) {
            float theta = 2.0f * PI * float(i) / float(segments);
            float x = r * cosf(theta);
            float y = r * sinf(theta);
            glVertex2f(x + cx, y + cy);
        }
        glEnd();
    }
}

// Function to draw detailed panel lines
void drawPanelLine(float x1, float y1, float x2, float y2, float width = 1.0f) {
    setColor(GRAY_DARK);
    glLineWidth(width);
    glBegin(GL_LINES);
    glVertex2f(x1, y1);
    glVertex2f(x2, y2);
    glEnd();
}

// Function to draw enhanced rectangular vent
void drawDetailedVent(float x, float y, float width, float height, int slits = 3) {
    // Main vent body
    std::vector<std::pair<float, float> > ventBody;
    ventBody.push_back(std::make_pair(x - width/2, y + height/2));
    ventBody.push_back(std::make_pair(x + width/2, y + height/2));
    ventBody.push_back(std::make_pair(x + width/2, y - height/2));
    ventBody.push_back(std::make_pair(x - width/2, y - height/2));

    drawFilledPolygon(ventBody, GRAY_DARK);

    // Individual slits
    float slitHeight = height * 0.6f / slits;
    float slitSpacing = height * 0.8f / (slits + 1);

    for (int i = 0; i < slits; ++i) {
        float slitY = y + height/2 - slitSpacing * (i + 1);
        std::vector<std::pair<float, float> > slit;
        slit.push_back(std::make_pair(x - width*0.35f, slitY + slitHeight/2));
        slit.push_back(std::make_pair(x + width*0.35f, slitY + slitHeight/2));
        slit.push_back(std::make_pair(x + width*0.35f, slitY - slitHeight/2));
        slit.push_back(std::make_pair(x - width*0.35f, slitY - slitHeight/2));

        drawFilledPolygon(slit, BLACK, false);
    }
}

/**
 * Menghasilkan titik untuk kurva Bezier kubik dan menggabungkannya dengan titik lain
 * untuk menggambar satu poligon tertutup. Digunakan untuk kubah kepala yang melengkung.
 */
void drawCurvedPolygon(const std::vector<std::pair<float, float> >& curve_pts, const std::vector<std::pair<float, float> >& line_pts, const Color& fillColor, int segments = 30) {
    std::vector<std::pair<float, float> > allPoints;

    // Menghasilkan titik untuk bagian kurva Bezier
    for (int i = 0; i <= segments; ++i) {
        float t = (float)i / (float)segments;
        float u = 1.0f - t;
        float tt = t * t;
        float uu = u * u;
        float uuu = uu * u;
        float ttt = tt * t;

        // Rumus Bezier kubik
        float x = uuu * curve_pts[0].first + 3 * uu * t * curve_pts[1].first + 3 * u * tt * curve_pts[2].first + ttt * curve_pts[3].first;
        float y = uuu * curve_pts[0].second + 3 * uu * t * curve_pts[1].second + 3 * u * tt * curve_pts[2].second + ttt * curve_pts[3].second;
        allPoints.push_back(std::make_pair(x, y));
    }

    // Menambahkan titik-titik garis lurus untuk menutup bentuk
    for(size_t i = 0; i < line_pts.size(); ++i) {
        allPoints.push_back(line_pts[i]);
    }

    drawFilledPolygon(allPoints, fillColor);
}

/**
 * Teknik Rekursif: Menggambar ventilasi samping kepala Gundam secara berulang.
 */
void drawSideVents(float startX, float startY, int count) {
    if (count <= 0) return;

    // Enhanced side vent with better proportions
    drawDetailedVent(startX + 0.025f, startY - 0.025f, 0.05f, 0.04f, 2);

    drawSideVents(startX - 0.015f, startY - 0.08f, count - 1);
}

// Enhanced mouth slits with better detail
void drawMouthSlits() {
    float centerX = 0.0f;
    float startY = -0.25f;

    // Main mouth grille area
    std::vector<std::pair<float, float> > mouthArea;
    mouthArea.push_back(std::make_pair(-0.12f, -0.22f));
    mouthArea.push_back(std::make_pair(0.12f, -0.22f));
    mouthArea.push_back(std::make_pair(0.1f, -0.42f));
    mouthArea.push_back(std::make_pair(-0.1f, -0.42f));

    drawFilledPolygon(mouthArea, GRAY_LIGHT);

    // Individual slits
    for (int i = 0; i < 5; ++i) {
        float y = startY - i * 0.035f;
        drawDetailedVent(centerX, y, 0.15f, 0.025f, 1);
    }
}

// Function to draw enhanced vulcan gun
void drawVulcanGun(float x, float y) {
    // Main gun housing
    std::vector<std::pair<float, float> > gunHousing;
    gunHousing.push_back(std::make_pair(x - 0.03f, y + 0.06f));
    gunHousing.push_back(std::make_pair(x + 0.02f, y + 0.08f));
    gunHousing.push_back(std::make_pair(x + 0.03f, y + 0.02f));
    gunHousing.push_back(std::make_pair(x - 0.02f, y));

    drawFilledPolygon(gunHousing, WHITE_SECONDARY);

    // Gun barrel
    drawFilledCircle(x, y + 0.04f, 0.025f, YELLOW_PRIMARY);
    drawFilledCircle(x, y + 0.04f, 0.015f, GRAY_DARK);
    drawFilledCircle(x, y + 0.04f, 0.008f, BLACK, false);
}

// Function to draw enhanced eye
void drawEnhancedEye(float centerX, float centerY, float width, float height) {
    // Main eye shape
    std::vector<std::pair<float, float> > eyeShape;
    eyeShape.push_back(std::make_pair(centerX - width/2, centerY + height/4));
    eyeShape.push_back(std::make_pair(centerX + width/2, centerY + height/3));
    eyeShape.push_back(std::make_pair(centerX + width/2, centerY - height/3));
    eyeShape.push_back(std::make_pair(centerX - width/2, centerY - height/4));

    drawFilledPolygon(eyeShape, YELLOW_PRIMARY);

    // Eye highlight
    std::vector<std::pair<float, float> > eyeHighlight;
    eyeHighlight.push_back(std::make_pair(centerX - width/3, centerY + height/6));
    eyeHighlight.push_back(std::make_pair(centerX + width/3, centerY + height/5));
    eyeHighlight.push_back(std::make_pair(centerX + width/3, centerY - height/5));
    eyeHighlight.push_back(std::make_pair(centerX - width/3, centerY - height/6));

    drawFilledPolygon(eyeHighlight, YELLOW_SECONDARY, false);
}

// --- LOGIKA GAMBAR UTAMA ---

void drawLeftHalf() {
    // 1. Kubah Kepala Utama (Putih) dengan atap melengkung (Kurva)
    {
        std::vector<std::pair<float, float> > curve_points;
        curve_points.push_back(std::make_pair(-0.4f, 0.3f));
        curve_points.push_back(std::make_pair(-0.4f, 0.75f));
        curve_points.push_back(std::make_pair(-0.1f, 0.8f));
        curve_points.push_back(std::make_pair(0.0f, 0.75f));

        std::vector<std::pair<float, float> > line_points;
        line_points.push_back(std::make_pair(0.0f, 0.1f));
        line_points.push_back(std::make_pair(-0.3f, 0.15f));

        drawCurvedPolygon(curve_points, line_points, WHITE_PRIMARY);
    }

    // 2. Sisi Helm (Putih dengan gradient)
    std::vector<std::pair<float, float> > helmet;
    helmet.push_back(std::make_pair(-0.4f, 0.3f));
    helmet.push_back(std::make_pair(-0.7f, 0.3f));
    helmet.push_back(std::make_pair(-0.75f, -0.4f));
    helmet.push_back(std::make_pair(-0.5f, -0.6f));
    helmet.push_back(std::make_pair(-0.3f, -0.5f));
    helmet.push_back(std::make_pair(-0.3f, 0.15f));

    drawGradientPolygon(helmet, WHITE_PRIMARY, WHITE_SECONDARY);

    // Panel lines on helmet
    drawPanelLine(-0.55f, 0.2f, -0.6f, -0.2f);
    drawPanelLine(-0.65f, 0.1f, -0.7f, -0.3f);

    // 3. Enhanced Vulcan Gun
    drawVulcanGun(-0.4f, 0.35f);

    // 4. Enhanced Side Ventilation
    drawSideVents(-0.62f, 0.1f, 4);

    // 5. Pelindung Pipi (Putih dengan detail)
    std::vector<std::pair<float, float> > cheek;
    cheek.push_back(std::make_pair(-0.3f, 0.15f));
    cheek.push_back(std::make_pair(-0.45f, 0.1f));
    cheek.push_back(std::make_pair(-0.4f, -0.3f));
    cheek.push_back(std::make_pair(-0.2f, -0.4f));
    cheek.push_back(std::make_pair(0.0f, -0.3f));
    cheek.push_back(std::make_pair(0.0f, 0.1f));

    drawFilledPolygon(cheek, WHITE_PRIMARY);

    // Cheek panel lines
    drawPanelLine(-0.25f, 0.05f, -0.3f, -0.2f);
    drawPanelLine(-0.15f, -0.1f, -0.25f, -0.35f);

    // 6. Enhanced Mouth Plate and Grille
    std::vector<std::pair<float, float> > mouthPlate;
    mouthPlate.push_back(std::make_pair(0.0f, -0.15f));
    mouthPlate.push_back(std::make_pair(-0.18f, -0.2f));
    mouthPlate.push_back(std::make_pair(-0.15f, -0.42f));
    mouthPlate.push_back(std::make_pair(0.0f, -0.4f));

    drawFilledPolygon(mouthPlate, GRAY_LIGHT);

    // Enhanced mouth slits
    drawMouthSlits();

    // 7. Enhanced Visor (Black with blue tint)
    std::vector<std::pair<float, float> > visor;
    visor.push_back(std::make_pair(0.0f, 0.15f));
    visor.push_back(std::make_pair(-0.4f, 0.15f));
    visor.push_back(std::make_pair(-0.35f, -0.05f));
    visor.push_back(std::make_pair(0.0f, -0.1f));

    drawFilledPolygon(visor, Color(0.05f, 0.05f, 0.2f), false);

    // Visor reflection
    std::vector<std::pair<float, float> > visorReflection;
    visorReflection.push_back(std::make_pair(-0.05f, 0.12f));
    visorReflection.push_back(std::make_pair(-0.35f, 0.12f));
    visorReflection.push_back(std::make_pair(-0.32f, 0.02f));
    visorReflection.push_back(std::make_pair(-0.05f, -0.02f));

    drawFilledPolygon(visorReflection, Color(0.1f, 0.1f, 0.4f), false);

    // 8. Enhanced Eyes
    drawEnhancedEye(-0.2f, 0.05f, 0.2f, 0.12f);

    // 9. Enhanced Chin (Red with gradient)
    std::vector<std::pair<float, float> > chin;
    chin.push_back(std::make_pair(0.0f, -0.3f));
    chin.push_back(std::make_pair(-0.15f, -0.4f));
    chin.push_back(std::make_pair(0.0f, -0.55f));

    drawGradientPolygon(chin, RED_PRIMARY, RED_SECONDARY);

    // 10. Enhanced Forehead Crest (Red with detail)
    std::vector<std::pair<float, float> > crest;
    crest.push_back(std::make_pair(0.0f, 0.15f));
    crest.push_back(std::make_pair(-0.18f, 0.3f));
    crest.push_back(std::make_pair(-0.18f, 0.45f));
    crest.push_back(std::make_pair(0.0f, 0.58f));

    drawGradientPolygon(crest, RED_PRIMARY, RED_SECONDARY);

    // Crest detail lines
    drawPanelLine(-0.05f, 0.25f, -0.15f, 0.4f);

    // V-Fin Base (Enhanced)
    std::vector<std::pair<float, float> > vfin_base;
    vfin_base.push_back(std::make_pair(-0.18f, 0.45f));
    vfin_base.push_back(std::make_pair(-0.2f, 0.46f));
    vfin_base.push_back(std::make_pair(-0.1f, 0.42f));
    vfin_base.push_back(std::make_pair(-0.08f, 0.4f));

    drawFilledPolygon(vfin_base, GRAY_DARK);

    // 11. Enhanced Forehead Camera
    std::vector<std::pair<float, float> > foreheadCam;
    foreheadCam.push_back(std::make_pair(0.0f, 0.58f));
    foreheadCam.push_back(std::make_pair(-0.1f, 0.6f));
    foreheadCam.push_back(std::make_pair(-0.1f, 0.7f));
    foreheadCam.push_back(std::make_pair(0.0f, 0.75f));

    drawFilledPolygon(foreheadCam, WHITE_PRIMARY);

    // Camera lens (Red center)
    std::vector<std::pair<float, float> > foreheadCamRed;
    foreheadCamRed.push_back(std::make_pair(0.0f, 0.62f));
    foreheadCamRed.push_back(std::make_pair(-0.08f, 0.64f));
    foreheadCamRed.push_back(std::make_pair(-0.08f, 0.68f));
    foreheadCamRed.push_back(std::make_pair(0.0f, 0.7f));

    drawFilledPolygon(foreheadCamRed, RED_PRIMARY, false);

    // Camera highlight
    drawFilledCircle(-0.04f, 0.66f, 0.015f, Color(1.0f, 0.8f, 0.8f), false);

    // 12. Enhanced V-Fin (Yellow with gradient)
    std::vector<std::pair<float, float> > vfin;
    vfin.push_back(std::make_pair(-0.1f, 0.42f));   // Bawah-dalam
    vfin.push_back(std::make_pair(-0.2f, 0.46f));  // Bawah-luar
    vfin.push_back(std::make_pair(-0.8f, 0.95f));   // Ujung-luar
    vfin.push_back(std::make_pair(-0.7f, 0.9f));    // Ujung-dalam

    drawGradientPolygon(vfin, YELLOW_PRIMARY, YELLOW_SECONDARY);

    // V-Fin detail line
    drawPanelLine(-0.15f, 0.44f, -0.75f, 0.92f);

    // Additional surface details
    drawPanelLine(-0.2f, 0.25f, 0.0f, 0.2f);  // Head center line
    drawPanelLine(-0.35f, 0.0f, -0.1f, -0.05f); // Eye area detail
}


// --- FUNGSI CALLBACK DAN SETUP GLUT ---

void display() {
    glClear(GL_COLOR_BUFFER_BIT);

    drawLeftHalf();

    glPushMatrix();
    glScalef(-1.0f, 1.0f, 1.0f);
    drawLeftHalf();
    glPopMatrix();

    glFlush();
}

void init() {
    // Enhanced background - darker space-like color
    glClearColor(0.05f, 0.05f, 0.1f, 1.0f);

    // Enable anti-aliasing for smoother lines
    glEnable(GL_LINE_SMOOTH);
    glEnable(GL_POLYGON_SMOOTH);
    glHint(GL_LINE_SMOOTH_HINT, GL_NICEST);
    glHint(GL_POLYGON_SMOOTH_HINT, GL_NICEST);

    // Enable blending for anti-aliasing
    glEnable(GL_BLEND);
    glBlendFunc(GL_SRC_ALPHA, GL_ONE_MINUS_SRC_ALPHA);

    glMatrixMode(GL_PROJECTION);
    glLoadIdentity();
    gluOrtho2D(-1.0, 1.0, -1.0, 1.0);
    glMatrixMode(GL_MODELVIEW);
}

int main(int argc, char** argv) {
    glutInit(&argc, argv);
    glutInitDisplayMode(GLUT_SINGLE | GLUT_RGB);
    glutInitWindowSize(800, 800);  // Larger window for better detail
    glutCreateWindow("Enhanced Gundam RX-78-2 Head - High Detail");
    init();
    glutDisplayFunc(display);
    glutMainLoop();
    return 0;
}

