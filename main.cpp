#include <windows.h>

 #include <GL/glut.h>

 #include <cmath>

 #include <vector>

 #include <utility>



 // Mendefinisikan konstanta PI untuk kalkulasi

 const float PI = 3.1415926f;



 // --- FUNGSI UTILITAS GAMBAR ---



 /**

  * Menggambar poligon berisi dari sebuah vektor titik.

  */

 void drawPolygon(const std::vector<std::pair<float, float> >& points) {

     glBegin(GL_POLYGON);

     for (size_t i = 0; i < points.size(); ++i) {

         glVertex2f(points[i].first, points[i].second);

     }

     glEnd();

 }



 /**

  * Menggambar garis luar poligon dari sebuah vektor titik.

  */

 void drawPolygonOutline(const std::vector<std::pair<float, float> >& points, float width = 2.0f) {

     glLineWidth(width);

     glColor3f(0.0f, 0.0f, 0.0f); // Garis luar hitam

     glBegin(GL_LINE_LOOP);

     for (size_t i = 0; i < points.size(); ++i) {

         glVertex2f(points[i].first, points[i].second);

     }

     glEnd();

 }



 /**

  * Menggambar lingkaran berisi.

  */

 void drawCircle(float cx, float cy, float r, int segments = 36) {

     glBegin(GL_POLYGON);

     for (int i = 0; i < segments; ++i) {

         float theta = 2.0f * PI * float(i) / float(segments);

         float x = r * cosf(theta);

         float y = r * sinf(theta);

         glVertex2f(x + cx, y + cy);

     }

     glEnd();

 }



 /**

  * Menghasilkan titik untuk kurva Bezier kubik dan menggabungkannya dengan titik lain

  * untuk menggambar satu poligon tertutup. Digunakan untuk kubah kepala yang melengkung.

  */

 void drawCurvedPolygon(const std::vector<std::pair<float, float> >& curve_pts, const std::vector<std::pair<float, float> >& line_pts, int segments = 30) {

     std::vector<std::pair<float, float> > allPoints;



     // Menghasilkan titik untuk bagian kurva Bezier

     for (int i = 0; i <= segments; ++i) {

         float t = (float)i / (float)segments;

         float u = 1.0f - t;

         float tt = t * t;

         float uu = u * u;

         float uuu = uu * u;

         float ttt = tt * t;



         // Rumus Bezier kubik

         float x = uuu * curve_pts[0].first + 3 * uu * t * curve_pts[1].first + 3 * u * tt * curve_pts[2].first + ttt * curve_pts[3].first;

         float y = uuu * curve_pts[0].second + 3 * uu * t * curve_pts[1].second + 3 * u * tt * curve_pts[2].second + ttt * curve_pts[3].second;

         allPoints.push_back(std::make_pair(x, y));

     }



     // Menambahkan titik-titik garis lurus untuk menutup bentuk

     for(size_t i = 0; i < line_pts.size(); ++i) {

         allPoints.push_back(line_pts[i]);

     }



     drawPolygon(allPoints);

     drawPolygonOutline(allPoints);

 }



 /**

  * Teknik Rekursif: Menggambar ventilasi samping kepala Gundam secara berulang.

  */

 void drawSideVents(float startX, float startY, int count) {

     if (count <= 0) return;



     std::vector<std::pair<float, float> > vent;

     vent.push_back(std::make_pair(startX, startY));

     vent.push_back(std::make_pair(startX + 0.05f, startY + 0.01f));

     vent.push_back(std::make_pair(startX + 0.05f, startY - 0.04f));

     vent.push_back(std::make_pair(startX, startY - 0.05f));



     glColor3f(0.0f, 0.0f, 0.0f);

     drawPolygon(vent);



     drawSideVents(startX - 0.015f, startY - 0.1f, count - 1);

 }





 // --- LOGIKA GAMBAR UTAMA ---



 void drawLeftHalf() {

     // 1. Kubah Kepala Utama (Putih) dengan atap melengkung (Kurva)

     {

         std::vector<std::pair<float, float> > curve_points;

         curve_points.push_back(std::make_pair(-0.4f, 0.3f));

         curve_points.push_back(std::make_pair(-0.4f, 0.75f));

         curve_points.push_back(std::make_pair(-0.1f, 0.8f));

         curve_points.push_back(std::make_pair(0.0f, 0.75f));



         std::vector<std::pair<float, float> > line_points;

         line_points.push_back(std::make_pair(0.0f, 0.1f));

         line_points.push_back(std::make_pair(-0.3f, 0.15f));



         glColor3f(1.0f, 1.0f, 1.0f);

         drawCurvedPolygon(curve_points, line_points);

     }



     // 2. Sisi Helm (Putih)

     std::vector<std::pair<float, float> > helmet;

     helmet.push_back(std::make_pair(-0.4f, 0.3f));

     helmet.push_back(std::make_pair(-0.7f, 0.3f));

     helmet.push_back(std::make_pair(-0.75f, -0.4f));

     helmet.push_back(std::make_pair(-0.5f, -0.6f));

     helmet.push_back(std::make_pair(-0.3f, -0.5f));

     helmet.push_back(std::make_pair(-0.3f, 0.15f));

     glColor3f(1.0f, 1.0f, 1.0f);

     drawPolygon(helmet);

     drawPolygonOutline(helmet);



     // 3. Pod Senapan Vulcan di Kepala (diperbarui)

     glColor3f(1.0f, 1.0f, 1.0f);

     std::vector<std::pair<float, float> > vulcan_base;

     vulcan_base.push_back(std::make_pair(-0.4f, 0.3f));

     vulcan_base.push_back(std::make_pair(-0.45f, 0.4f));

     vulcan_base.push_back(std::make_pair(-0.4f, 0.45f));

     drawPolygon(vulcan_base);

     drawPolygonOutline(vulcan_base);



     glColor3f(1.0f, 1.0f, 0.0f);

     drawCircle(-0.4f, 0.35f, 0.025f);

     // Reset line width after circle drawing

     glColor3f(0.0f, 0.0f, 0.0f);

     drawCircle(-0.4f, 0.35f, 0.01f);



     // 4. Ventilasi Samping

     drawSideVents(-0.62f, 0.1f, 4);



     // 5. Pelindung Pipi (Putih)

     std::vector<std::pair<float, float> > cheek;

     cheek.push_back(std::make_pair(-0.3f, 0.15f));

     cheek.push_back(std::make_pair(-0.45f, 0.1f));

     cheek.push_back(std::make_pair(-0.4f, -0.3f));

     cheek.push_back(std::make_pair(-0.2f, -0.4f));

     cheek.push_back(std::make_pair(0.0f, -0.3f));

     cheek.push_back(std::make_pair(0.0f, 0.1f));

     glColor3f(1.0f, 1.0f, 1.0f);

     drawPolygon(cheek);

     drawPolygonOutline(cheek);



     // 6. Pelat Mulut (Abu-abu)

     std::vector<std::pair<float, float> > mouthPlate;

     mouthPlate.push_back(std::make_pair(0.0f, -0.15f));

     mouthPlate.push_back(std::make_pair(-0.18f, -0.2f));

     mouthPlate.push_back(std::make_pair(-0.15f, -0.42f));

     mouthPlate.push_back(std::make_pair(0.0f, -0.4f));

     glColor3f(0.8f, 0.8f, 0.8f);

     drawPolygon(mouthPlate);

     drawPolygonOutline(mouthPlate);



     // Ventilasi pada Pelat Mulut (diperbarui)

     std::vector<std::pair<float, float> > mouth_vent_left;

     mouth_vent_left.push_back(std::make_pair(-0.08f, -0.25f));

     mouth_vent_left.push_back(std::make_pair(-0.12f, -0.26f));

     mouth_vent_left.push_back(std::make_pair(-0.11f, -0.28f));

     mouth_vent_left.push_back(std::make_pair(-0.07f, -0.27f));

     glColor3f(0.0f,0.0f,0.0f);

     drawPolygon(mouth_vent_left);



     // 7. Visor Hitam (untuk mata)

     std::vector<std::pair<float, float> > visor;

     visor.push_back(std::make_pair(0.0f, 0.15f));

     visor.push_back(std::make_pair(-0.4f, 0.15f));

     visor.push_back(std::make_pair(-0.35f, -0.05f));

     visor.push_back(std::make_pair(0.0f, -0.1f));

     glColor3f(0.0f, 0.0f, 0.0f);

     drawPolygon(visor);



     // 8. Mata (Kuning)

     std::vector<std::pair<float, float> > eye;

     eye.push_back(std::make_pair(-0.3f, 0.1f));

     eye.push_back(std::make_pair(-0.1f, 0.08f));

     eye.push_back(std::make_pair(-0.15f, -0.02f));

     eye.push_back(std::make_pair(-0.3f, 0.0f));

     glColor3f(1.0f, 1.0f, 0.0f); // Kuning

     drawPolygon(eye);

     drawPolygonOutline(eye);



     // 9. Dagu (Merah)

     std::vector<std::pair<float, float> > chin;

     chin.push_back(std::make_pair(0.0f, -0.3f));

     chin.push_back(std::make_pair(-0.15f, -0.4f));

     chin.push_back(std::make_pair(0.0f, -0.55f));

     glColor3f(1.0f, 0.0f, 0.0f);

     drawPolygon(chin);

     drawPolygonOutline(chin);



     // 10. Lambang Dahi (Merah) - Bentuk diperbarui

     std::vector<std::pair<float, float> > crest;

     crest.push_back(std::make_pair(0.0f, 0.15f));

     crest.push_back(std::make_pair(-0.18f, 0.3f));

     crest.push_back(std::make_pair(-0.18f, 0.45f));

     crest.push_back(std::make_pair(0.0f, 0.58f));

     glColor3f(1.0f, 0.0f, 0.0f);

     drawPolygon(crest);

     drawPolygonOutline(crest);



     // Dasar V-Fin (Hitam) - bagian baru

     std::vector<std::pair<float, float> > vfin_base;

     vfin_base.push_back(std::make_pair(-0.18f, 0.45f));

     vfin_base.push_back(std::make_pair(-0.2f, 0.46f));

     vfin_base.push_back(std::make_pair(-0.1f, 0.42f));

     vfin_base.push_back(std::make_pair(-0.08f, 0.4f));

     glColor3f(0.0f, 0.0f, 0.0f);

     drawPolygon(vfin_base);





     // 11. Blok Kamera Dahi (Putih dengan pusat Merah) - Bentuk diperbarui

     std::vector<std::pair<float, float> > foreheadCam;

     foreheadCam.push_back(std::make_pair(0.0f, 0.58f));

     foreheadCam.push_back(std::make_pair(-0.1f, 0.6f));

     foreheadCam.push_back(std::make_pair(-0.1f, 0.7f));

     foreheadCam.push_back(std::make_pair(0.0f, 0.75f));

     glColor3f(1.0f, 1.0f, 1.0f);

     drawPolygon(foreheadCam);

     drawPolygonOutline(foreheadCam);



     // Bagian merah dari kamera

     std::vector<std::pair<float, float> > foreheadCamRed;

     foreheadCamRed.push_back(std::make_pair(0.0f, 0.62f));

     foreheadCamRed.push_back(std::make_pair(-0.08f, 0.64f));

     foreheadCamRed.push_back(std::make_pair(-0.08f, 0.68f));

     foreheadCamRed.push_back(std::make_pair(0.0f, 0.7f));

     glColor3f(1.0f, 0.0f, 0.0f);

     drawPolygon(foreheadCamRed);



     // 12. V-Fin (Kuning) - Bentuk diperbarui

     std::vector<std::pair<float, float> > vfin;

     vfin.push_back(std::make_pair(-0.1f, 0.42f));   // Bawah-dalam

     vfin.push_back(std::make_pair(-0.2f, 0.46f));  // Bawah-luar

     vfin.push_back(std::make_pair(-0.8f, 0.95f));   // Ujung-luar

     vfin.push_back(std::make_pair(-0.7f, 0.9f));    // Ujung-dalam

     glColor3f(1.0f, 1.0f, 0.0f);

     drawPolygon(vfin);

     drawPolygonOutline(vfin);

 }





 // --- FUNGSI CALLBACK DAN SETUP GLUT ---



 void display() {

     glClear(GL_COLOR_BUFFER_BIT);



     drawLeftHalf();



     glPushMatrix();

     glScalef(-1.0f, 1.0f, 1.0f);

     drawLeftHalf();

     glPopMatrix();



     glFlush();

 }



 void init() {

     glClearColor(0.1f, 0.1f, 0.1f, 1.0f);

     glMatrixMode(GL_PROJECTION);

     glLoadIdentity();

     gluOrtho2D(-1.0, 1.0, -1.0, 1.0);

     glMatrixMode(GL_MODELVIEW);

 }



 int main(int argc, char** argv) {

     glutInit(&argc, argv);

     glutInitDisplayMode(GLUT_SINGLE | GLUT_RGB);

     glutInitWindowSize(600, 600);

     glutCreateWindow("Kepala Gundam RX-78-2");

     init();

     glutDisplayFunc(display);

     glutMainLoop();

     return 0;

 }

