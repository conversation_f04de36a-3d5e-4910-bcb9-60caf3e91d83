// Simple OpenGL test program
// Compile with: g++ -I./freeglut/include test_opengl.cpp -o test.exe -L./freeglut/lib -lfreeglut -lopengl32 -lglu32 -lwinmm -lgdi32

#include <windows.h>
#include <GL/glut.h>

void display() {
    glClear(GL_COLOR_BUFFER_BIT);
    
    // Draw a simple triangle
    glColor3f(1.0f, 0.0f, 0.0f); // Red
    glBegin(GL_TRIANGLES);
        glVertex2f(0.0f, 0.5f);   // Top
        glVertex2f(-0.5f, -0.5f); // Bottom left
        glVertex2f(0.5f, -0.5f);  // Bottom right
    glEnd();
    
    glFlush();
}

void init() {
    glClearColor(0.0f, 0.0f, 0.0f, 1.0f); // Black background
    glMatrixMode(GL_PROJECTION);
    glLoadIdentity();
    gluOrtho2D(-1.0, 1.0, -1.0, 1.0);
    glMatrixMode(GL_MODELVIEW);
}

int main(int argc, char** argv) {
    glutInit(&argc, argv);
    glutInitDisplayMode(GLUT_SINGLE | GLUT_RGB);
    glutInitWindowSize(400, 400);
    glutCreateWindow("OpenGL Test - Red Triangle");
    init();
    glutDisplayFunc(display);
    glutMainLoop();
    return 0;
}
