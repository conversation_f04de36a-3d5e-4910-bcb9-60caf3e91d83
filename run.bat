@echo off
echo Running Gundam Head Project...
echo.

REM Check if executable exists
if not exist "GundamHead.exe" (
    echo GundamHead.exe not found!
    echo Building project first...
    call build.bat
    if errorlevel 1 (
        echo Build failed!
        pause
        exit /b 1
    )
)

REM Check if FreeGLUT DLL is needed
if exist "freeglut\bin\freeglut.dll" (
    if not exist "freeglut.dll" (
        echo Copying FreeGLUT DLL...
        copy "freeglut\bin\freeglut.dll" . >nul
    )
)

REM Run the program
echo Starting Gundam Head visualization...
GundamHead.exe

echo.
echo Program finished.
pause
