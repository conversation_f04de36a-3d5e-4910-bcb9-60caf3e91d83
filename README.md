# Gundam RX-78-2 Head Project

Proyek ini menampilkan kepala Gundam RX-78-2 menggunakan OpenGL dan FreeGLUT dengan teknik computer graphics.

## Deskripsi Proyek

Kepala Gundam RX-78-2 terdiri dari:
- **Persegi** - Untuk bagian-bagian struktural helm
- **Segitiga** - Untuk V-fin dan detail geometris
- **Polygon** - Untuk bentuk kompleks seperti pelindung pipi
- **Kurva** - Menggunakan Bezier curves untuk kubah kepala yang melengkung
- **Line** - Untuk outline dan detail panel
- **Lingkaran** - Untu<PERSON> mata, vulcan gun, dan kamera

## Teknik yang Digunakan

1. **Mirroring**: Keseluruhan kepala gundam simetris, jadi setengah kepala di-mirror untuk mendapatkan kepala lengkap
2. **Skala**: Beberapa bentuk menggunakan scaling untuk ukuran yang berbeda
3. **Rotasi**: Segitiga pada V-fin dirotasi agar sesuai posisi
4. **Rekursif**: Digunakan pada ventilasi samping kepala gundam
5. **Translasi**: Untuk penempatan objek-objek pada kepala gundam

## Hasil Akhir (Tema)

Gambar menampilkan kepala Gundam RX-78-2 dengan gaya simpel seperti emoji. Kepala yang simetris membuatnya lebih mudah di-render.

## Setup dan Kompilasi

### Prasyarat

1. **MinGW-w64** atau **MSYS2** dengan g++ compiler
2. **FreeGLUT** library (sudah disediakan dalam folder `freeglut/`)
3. **Visual Studio Code** dengan ekstensi C/C++

### Struktur Folder

```
project/
├── main.cpp              # File utama program
├── freeglut/             # FreeGLUT library
│   ├── include/          # Header files
│   └── lib/              # Library files
├── .vscode/              # VS Code configuration
│   ├── tasks.json        # Build tasks
│   ├── c_cpp_properties.json
│   └── launch.json
├── CMakeLists.txt        # CMake configuration
├── Makefile              # Make configuration
└── README.md             # File ini
```

### Cara Kompilasi

#### Opsi 1: Menggunakan VS Code (Recommended)

1. Buka folder project di VS Code
2. Tekan `Ctrl+Shift+P` dan pilih "Tasks: Run Build Task"
3. Pilih "Build Gundam Head Project"
4. Atau tekan `Ctrl+Shift+B` untuk build langsung

#### Opsi 2: Menggunakan Command Line dengan Makefile

```bash
# Build project
make

# Build dan run
make run

# Clean build files
make clean
```

#### Opsi 3: Menggunakan CMake

```bash
# Configure
cmake -B build -S .

# Build
cmake --build build --config Debug

# Run
./build/GundamHead.exe
```

#### Opsi 4: Manual dengan g++

```bash
g++ -std=c++11 -Wall -g -I./freeglut/include main.cpp -o GundamHead.exe -L./freeglut/lib -lfreeglut -lopengl32 -lglu32 -lwinmm -lgdi32
```

### Menjalankan Program

Setelah kompilasi berhasil, jalankan:

```bash
./GundamHead.exe
```

Atau di VS Code, gunakan task "Run Gundam Head".

## Troubleshooting

### Error: "freeglut.h: No such file or directory"

- Pastikan folder `freeglut/` ada di root project
- Pastikan struktur folder `freeglut/include/GL/freeglut.h` benar

### Error: "cannot find -lfreeglut"

- Pastikan file `freeglut.lib` atau `libfreeglut.a` ada di `freeglut/lib/`
- Periksa apakah library sesuai dengan arsitektur compiler (32-bit vs 64-bit)

### Error: "The program can't start because freeglut.dll is missing"

- Copy file `freeglut.dll` dari `freeglut/bin/` ke folder project
- Atau tambahkan `freeglut/bin/` ke PATH environment variable

### Compiler tidak ditemukan

- Install MinGW-w64 atau MSYS2
- Pastikan `g++` ada di PATH
- Di VS Code, periksa setting compiler path di `c_cpp_properties.json`

## Kontrol Program

- **ESC**: Keluar dari program
- **Mouse**: Tidak ada interaksi mouse (static display)
- **Keyboard**: Tidak ada kontrol keyboard tambahan

## Fitur Teknis

- **Anti-aliasing**: Untuk garis yang lebih halus
- **Mirroring**: Rendering efisien dengan symmetry
- **Bezier Curves**: Untuk bentuk organik pada kubah kepala
- **Recursive Drawing**: Untuk pola ventilasi yang berulang
- **Polygon Tessellation**: Untuk bentuk kompleks

## Author

Proyek Computer Graphics - Kepala Gundam RX-78-2

## License

Educational use only.
