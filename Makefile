# Makefile for Gundam Head Project
# Kompilasi untuk Windows dengan FreeGLUT

# Compiler
CXX = g++

# Compiler flags
CXXFLAGS = -std=c++11 -Wall -g

# Include directories
INCLUDES = -I./freeglut/include

# Library directories
LIBDIRS = -L./freeglut/lib

# Libraries to link
LIBS = -lfreeglut -lopengl32 -lglu32 -lwinmm -lgdi32

# Source files
SOURCES = main.cpp

# Output executable
TARGET = GundamHead.exe

# Default target
all: $(TARGET)

# Build target
$(TARGET): $(SOURCES)
	$(CXX) $(CXXFLAGS) $(INCLUDES) $(SOURCES) -o $(TARGET) $(LIBDIRS) $(LIBS)

# Clean target
clean:
	del /f $(TARGET) *.o

# Run target
run: $(TARGET)
	./$(TARGET)

# Help target
help:
	@echo "Available targets:"
	@echo "  all     - Build the project (default)"
	@echo "  clean   - Remove built files"
	@echo "  run     - Build and run the project"
	@echo "  help    - Show this help message"

.PHONY: all clean run help
